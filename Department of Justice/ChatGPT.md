Short Form Application

Software Developer – Department of Justice, Tasmania

Dear Hiring Panel,

I am writing to express my interest in the Software Developer position within the Department of Justice, Tasmania. I have over two decades of experience in software development, system integration, and technical leadership, with particular strengths in managing sensitive data, coordinating with vendors, and supporting mission-critical enterprise systems. My past roles in local government and engineering consultancy have given me direct experience with secure systems, high-level development, and support responsibilities that align closely with the Department’s needs.

**Source Code Management & Secure Development**

At Devonport City Council, I provided software development services to integrate various enterprise systems, including Technology One, ArcGIS, and the TRIM records management system. As part of Council’s IT team, I handled sensitive data, ensuring appropriate controls around access and visibility. For example, when exposing cemetery search data to the public, I created an API backed by T1 Property & Rating that only returned non-sensitive fields (first name, last name, and plot). This endpoint was secured via a reverse proxy and published through Microsoft ISA Server, enabling safe public access while preserving data integrity and privacy.

In my role at pitt&sherry, I worked on AssetAsyst, a management system for transport and structural assets used by local governments, TasRail and DSG. As the second developer, I quickly gained a deep understanding of the business domain, which included engineering concepts like condition-based inspections, predictive modelling, and load analysis. Within six months I was able to independently support the system, allowing the primary developer to take extended leave. In under two years, I became fully proficient across all areas of the system, forming a two-person team with shared ownership and autonomy over development and delivery.

**Vendor Engagement & Technical Oversight**

At Devonport City Council, I frequently worked with vendors such as Technology One to ensure that custom queries, stored procedures, or software changes remained within supported frameworks. I was responsible for vetting changes, liaising with vendors, and managing outsourced development work. In many cases, consultants worked onsite, and I provided oversight to align their work with Council’s objectives and policies.

Similarly, at pitt&sherry I led the architectural re-design of AssetAsyst, transforming it from a desktop-based client/server product into a cloud-native platform. This transition supported the business’s move into Smart Assets (digital twins, sensor integration, and analytics) and enabled relevance in a competitive market. I modelled the domain, designed the new system architecture, and supported developers adapting to modern web and cloud technologies. For example, by adopting Blazor Server, developers could remain productive using C# while gradually building confidence with JavaScript, HTML, and CSS. This also allowed me to provide direct mentoring and support.

**Documentation, Quality Assurance & Tooling**

Software quality and maintainability were critical to our success. At pitt&sherry, we enforced strict version control and code review practices using GitHub. Changes could only be merged into the main branch through Pull Requests, supported by automated workflows using GitHub Actions. Linters ensured consistency and code quality across style, correctness, and security. Tests (unit, integration, functional, and performance) were run automatically on every change. Reviewers could test in isolated environments, and Continuous Deployment pipelines ensured that only validated changes reached production.

This structured workflow improved collaboration and resilience. If issues emerged in production, they were addressed via a new Pull Request — the same process was followed for fixes to ensure quality and traceability. I also contributed to technical documentation, developer onboarding guides, and change logs, ensuring that knowledge was shared and preserved.

**Data Management, Support & Incident Resolution**

During my time at Devonport City Council, I provided data analysis and reporting services to stakeholders across departments — including the General Manager, Accountant, and Rates Officer. Many reports relied on complex SQL queries, tailored to role-based access. I also assisted with database administration, working with the Systems Administrator to manage and restore databases. For instance, we used Point-In-Time Recovery with SQL Server (configured in Full recovery mode) to restore the T1 Property & Rating database and limit data loss to under 30 minutes.

I regularly responded to incidents and support requests, especially those that escalated beyond first-line resolution. My combination of development and operational knowledge meant I could diagnose and resolve complex system-level issues and guide other developers or stakeholders through technical risks and mitigation strategies.

**Leadership, Modernisation & Strategic Advice**

As AssetAsyst evolved, I provided guidance across all phases of the redevelopment — including ETL processes, domain modelling, database design, and identity management. The new system used Azure SQL and Azure Storage for a multi-tenant architecture, and supported Single Sign-On via Azure AD B2C. I implemented a custom policy framework allowing customers to link their Entra ID tenants and manage their users using a SCIM service.

These changes required not only technical execution but also strategic alignment with business needs, project planning, and regular communication with both internal stakeholders and external partners. I was often called upon to evaluate new technologies and frameworks, and to recommend tools and approaches that supported maintainability and scalability in a regulated environment.

I believe my experience aligns closely with the Department’s Statement of Duties — particularly in source code management, high-level system development, vendor coordination, and secure data handling. I would welcome the opportunity to contribute to the Department’s goals and bring my skills to a team focused on public service, innovation, and accountability.

Thank you for considering my application. I have attached my current résumé and am available to provide further information or attend an interview at your convenience.

Kind regards,
Hamish Murphy
