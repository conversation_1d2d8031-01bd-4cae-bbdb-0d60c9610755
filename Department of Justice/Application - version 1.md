# Manage the software code for systems within the Department.

At pitt&sherry we moved our product AssetAsyst from a desktop client/server system to a cloud-native platform.

At pitt&sherry we re-wrote our main product AssetAsyst into a cloud-native application. This was a strategic decision to increase the relevance of AssetAsyst for it's customer-base (mainly local government). There were a lot of moving parts - a web server, an API server, Azure Functions, an admin console, common libraries, object models, DTOs, etc. Because we had a small team (2 to 3 developers) we needed to move quickly and reduce friction by allowing the developers to run code using the most convenient method. Therefore the rewrite was implemented as a monolith - keeping as much code as possible in the one repository. Using Visual Studio, multiple Projects are in the one Solution - as a dev it's possible to run multiple projects simultaniously (e.g. API server and Azure Functions). Deployment is managed through GitHub Actions - pushing a commit to a Pull Request would deploy code to various services in Microsoft Azure in the staging environment. Pushing a commit to the main branch triggered deployments to production. The act of running dev services on your machine or pushing updates to the cloud is quite expensive - but very convenient for a small team. If the team grows or this causes problems for deployment, the monolith will be broken up.

In contrast, at Syngengco we implemented a microservices architecture - to support the polyglot nature of the services for the solution. As a result, each service has its own repository. Which means that more time was spent on virtual infrastructure and deployment. Each repository has its own Docker configuration and deployment was handled via Terraform. In the case of the developer, they could use Terraform to setup and run the appropriate services in their local Docker server. For testing, staging and production, GitLab CI/CD would use Terraform to push updates to an internal Docker server or Microsoft Azure. Having a repository per service keeps the code simple for that service. However, effectively coordinating services in the right context added to the overall work load of the project. The advantage was that with time and the right automation, the solution could grow more effectively as the team grew.

# Undertake high level modification, development, and management of source code to meet Agency objectives from either support related issues or specifications that detail business requirements in conjunction with outsourced contracted development services.

Working on AssetAsyst at pitt&sherry I started learning the code base one feature at a time. Through this process, I gradually I accumulated knowledge of business domain topics such as Structural Engineering. I moved quickly from

# Provide authoritative advice and technical support and backup for the Principal Software Developer when required that includes taking the lead in nominated projects.

# Create or modify system documentation in conjunction with members of the Enterprise Systems Support team and outsourced contracted development services.

# Respond and manage escalated incidents and find resolutions to complex matters.

# Research and recommend tools and emerging technology to support strategies and methodologies in the development, delivery and change processes associated with in-house applications and databases.

# Undertake high-level data base administration and user support in consultation with the other members of the Enterprise Systems Support team.

# Represents the Department in liaising with vendor and developing solutions.
