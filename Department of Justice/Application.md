While at the Devonport City Council I provided software development services integrating Council's various systems - this included Technology One, ArcGIS and records management system TRIM. As a member of the IT department and a software developer, I had access to sensitive data - this included information about the general public and the inner workings of the Council. When handling and presenting data - it was critical that I treated that data with care, ensuring that data wasn't retrieved when it wasn't necessary and that data was presented to the correct/authorised user. For example, data for the cemetery search was accessible via the public website. The data source was the SQL Server backend for T1 Property & Rating. I created an API that would only return relevant data (i.e. first name, last name and plot). Working with the System Administrator we protected this API behind a reverse proxy that was exposed as a public endpoint via Council's Microsoft ISA Server. The public website could then retrieve data that was only intended for the general public.

While at Council I provided data analysis services for various departments (including the General Manager, Accountant, Rates Officer and Works & Assets). This would often be in the form of complex custom queries that were exposed as reports that only they could access. In some cases, I altered vendor queries and/or Stored Procedures. In these cases I worked with the vendor to veto the work to ensure that I operated within the framework of their product - i.e. that what I created did not step outside their guidelines and did not jeopardise the support agreement. Likewise I was responsible for managing Technology One consultants when we outsourced their services for software development - in these cases a consultant would usually be onsite.

I also provided system administrative services and I would work with the System Administrator to manage, maintain and backup our database servers. For example, I provided advice on the restoration of the SQL Server database for T1 Property & Rating - that database was configured for the Full recovery model, using Point-In-Time Recovery we were able to minimise data loss to within a half hour.

At pitt&sherry I worked on AssetAsyst - a Management Information System for network assets such as bridges and roads. The business domain behind this product is based on Structural Engineering concepts. Learning engineering concepts such as condition-based inspections, predictive modelling and heavy vehicle analysis took time. However, I stepped through the product working on one feature at a time. I also provided customer support (to local governments across Australia and other organisations, such as Tasrail and DSG). Resolving issues raised by customers and providing support resulted in deeper understanding of the product in a condensed period of time. Joining as the second developer, within 6 months I was able to support the system such that the main developer could go on leave. Within 18 months I had commanding knowledge of the business domain and the entire system. Effectively, we became a interchangeable two person team that could on any part of the system. It increased the autonomy of the team as a whole - we were able to push the software forward on two fronts.

Quality assurance is a fundamental practice at pitt&sherry - we used GitHub to manage code changes. For example, code was not allowed to be merged into the main branch unless it was through a Pull Request. This was enforced through GitHub configuration (e.g. branch protection, repository access through user and group permissions). Code changes were implemented via a feature branch - the author would be responsible for testing their own code and writing relevant documentation before requesting a review via the Pull Request. Ideally, the change should be as small as possible - reduced complexity makes it easier for the reviewer to consume and critique (increasing the quality of the review). This process was further supported by relevant tooling - for example, a test harness for unit, integration tests, functional tests, performance, stress/load and code coverage. Linters provided a means to check style, code quality, security and correctness. These features were configured to work within Visual Studio on the developers PC. However, they were automated via GitHub Actions and were surfaced as checks for the Pull Request. In the case of a Pull Request, GitHub Actions were setup to create a test environment for the convenience of the reviewer - it was their role to review and test the changes of the Pull Request to ensure that the changes worked as intended. A Pull Request could only be merged if it were approved and all checks passed. Once a commit is created on the main branch, then a similar process is triggered via GitHub Actions - if all checks pass, then the changes would be deployed to production (Continuous Deployment). If there was an issue in production, then we would implement a fix through another Pull Request and repeat the process.

At pitt&sherry I provided guidance and support with regards to technical leadership. Originally, AssetAsyst was implemented as an on-premise desktop-based client/server system. However, the business wanted to move into Smart Assets - digital twins, sensors, data analytics and business intelligence. To facilitate this move and remain relevant in the local government market, AssetAsyst was re-written as a cloud-native solution. I was able to model the domain, architect the new cloud platform and guide developers who were coming to terms with cloud technologies and modern web development. For example, implementing the web app with Blazor Server meant that our developers could be productive sooner - they could lean on C#, making it practical for me to provide support with regards to JavaScript, HTML and CSS. Migration to cloud included an ETL process - where on-premise customer data was imported into a consolidated multi-tenant data store (Azure SQL Server and Azure Storage). Customers could login to the system via SSO with Azure AD B2C - a custom policy allowed organisations to link their Entra ID tenant to AssetAsyst. Customers could then manage their own users via a SCIM service.



For example, I resolved a synchronisation issue for Aurizon - when mobile users returned to base, the database would lock up preventing the synchronisation process from completing. As it turns out, the data being synchronised wasn't wrapped up in a transaction like it was designed to.
