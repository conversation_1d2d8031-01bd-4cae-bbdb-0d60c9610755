# Manage the software code for systems within the Department.

At pitt&sherry we worked with GitHub to implement change management to support quality assurance processes. For example, code was not allowed to be merged into the main branch unless it was through a Pull Request. This was enforced through GitHub configuration (e.g. branch protection, repository access through user and group permissions). Code changes were implemented via a feature branch - the author would be responsible for testing their code before requesting a review via the Pull Request. Ideally, the change should be a small as possible - reduced complexity makes it easier for the reviewer to consume and critique (increasing the quality of the review). This process was further supported by relevant tooling - for example, a test harness for unit, integration tests, functional tests, performance, stress/load and code coverage. Linters provided a means to check style, code quality, security and correctness. These features were configured to work within Visual Studio on the developers PC. However, they were automated via GitHub Actions and were surfaced as checks for the Pull Request. In the case of a Pull Request, GitHub Actions were setup to create a test environment for the convenience of the reviewer - it was their role to review and test the changes of the Pull Request to ensure that the changes worked as intended. A Pull Request could only be merged if it were approved and all checks passed.

Integrated documentation. Would have used enterprise security if pitt&sherry would have paid for it. Used managed systems where possible to support automated patches.

At pitt&sherry the main code repository was implemented as a monolith. This was a decision of convenience to make it easy for each developer to run core applications (e.g. the web app, the API server and Azure Functions). It was also used for deployment to the cloud. When code is merged into the main branch, then after all checks passed, GitHub Actions would automate the deployment of these services to Microsoft Azure. Care was taken to ensure that this did not disrupt services. For example, blue/green deploys ensured that existing users of the web app were not affected as the deployment transitioned to the new server.

Synengco were going to pay for enterprise security (maybe mention it as a difference to pitt&sherry)

In contrast, I worked on a microservice/polyglot architecture at Synengco. Each service has its own repository, with its own tests and linters (relevant to the language it was implemented in) and its own deployment (through GitLab CI/CD). However, this required more attention to the infrastructure between these services. Each service was containerised so that it could be deployed to a Docker server (for testing/staging) or Azure (for production). Management of this infrastucture was handled through Terraform.

Designing systems to work with

Cross service integration and ensuring that nothing breaks? Did work with Vendor solutions? Gained insight into how DCC systems integrated with each other. Kept data private.

# Undertake high level modification, development, and management of source code to meet Agency objectives from either support related issues or specifications that detail business requirements in conjunction with outsourced contracted development services.

When I first started working on AssetAsyst,

# Provide authoritative advice and technical support and backup for the Principal Software Developer when required that includes taking the lead in nominated projects.

Basicaly 2IC and will know enough of the subject matter to step into their shoes while they're away. Also be entirely responsible for your own things, including being responsible for technology choices, contractors, stakeholders and take accountability of outcomes

# Create or modify system documentation in conjunction with members of the Enterprise Systems Support team and outsourced contracted development services.

Do documentation. Liaise with internal IT/support staff to identify points of contention. Make sure contractors create effective documentation

# Respond and manage escalated incidents and find resolutions to complex matters.

# Research and recommend tools and emerging technology to support strategies and methodologies in the development, delivery and change processes associated with in-house applications and databases.

Technology innovation advisor to help keep the department modern and efficient

# Undertake high-level data base administration and user support in consultation with the other members of the Enterprise Systems Support team.

While working at the Devonport City Council I liaised with the system administrator with regards to Council's SQL Server instances. In most cases, databases were setup to support existing enterprise services (such as the products supplied by Technology One). However, in cases where

Originally, AssetAsyst (pitt&sherry) was implemented as a on-premise client/server installation. AssetAsyst provided documentation on various configurations that could be supported onsite. For example, we would provide the minimum spec that could be used to run the SQL Server database efficiently. It was common for us to work with a new client (often a local government agency) to help them with the details of this installation. Often we would fallback to the documentation itself for reference, but we would help with

# Represents the Department in liaising with vendor and developing solutions.
